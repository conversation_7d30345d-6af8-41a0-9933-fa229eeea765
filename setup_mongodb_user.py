#!/usr/bin/env python3
"""
Setup MongoDB user for the application
"""

import os
from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, OperationFailure
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def setup_mongodb_user():
    """Setup MongoDB user with proper permissions"""
    
    # Get MongoDB settings from environment
    host = os.environ.get('MONGODB_HOST', 'localhost')
    port = int(os.environ.get('MONGODB_PORT', 27017))
    username = os.environ.get('MONGODB_USERNAME', '')
    password = os.environ.get('MONGODB_PASSWORD', '')
    auth_source = os.environ.get('MONGODB_AUTH_SOURCE', 'admin')
    db_name = os.environ.get('MONGODB_DB', 'rtrda')
    
    print(f"Setting up MongoDB user:")
    print(f"  Host: {host}")
    print(f"  Port: {port}")
    print(f"  Username: {username}")
    print(f"  Database: {db_name}")
    print(f"  Auth Source: {auth_source}")
    print()
    
    try:
        # Connect without authentication first
        print("1. Connecting to MongoDB without authentication...")
        client = MongoClient(f"mongodb://{host}:{port}", serverSelectionTimeoutMS=5000)
        client.admin.command('ping')
        print("   ✓ Connected successfully")
        
        # Check if authentication is enabled
        try:
            users = client.admin.command("usersInfo")
            print(f"   ✓ Found {len(users.get('users', []))} users in admin database")
        except OperationFailure as e:
            if "not authorized" in str(e).lower():
                print("   ! Authentication is enabled but we're connected without auth")
            else:
                print(f"   ! Error checking users: {e}")
        
        # Try to list users in admin database
        try:
            admin_db = client.admin
            users_info = admin_db.command("usersInfo")
            existing_users = [user['user'] for user in users_info.get('users', [])]
            print(f"   Existing users in admin database: {existing_users}")
            
            if username in existing_users:
                print(f"   ✓ User '{username}' already exists")
            else:
                print(f"   ! User '{username}' does not exist")
                
                # Create the user
                print(f"2. Creating user '{username}'...")
                admin_db.command("createUser", username, pwd=password, roles=[
                    {"role": "readWrite", "db": db_name},
                    {"role": "dbAdmin", "db": db_name},
                    {"role": "readWrite", "db": "admin"}
                ])
                print(f"   ✓ User '{username}' created successfully")
                
        except OperationFailure as e:
            if "not authorized" in str(e).lower():
                print("   ! Cannot create user - not authorized (authentication may be enabled)")
                print("   You may need to:")
                print("   1. Connect as an admin user to create the user")
                print("   2. Or disable authentication temporarily")
            else:
                print(f"   ! Error managing users: {e}")
        
        # Test the target database
        try:
            target_db = client[db_name]
            collections = target_db.list_collection_names()
            print(f"   ✓ Target database '{db_name}' accessible, {len(collections)} collections")
        except Exception as e:
            print(f"   ! Error accessing target database: {e}")
        
        client.close()
        
    except Exception as e:
        print(f"   ✗ Connection failed: {e}")
        return False
    
    # Now test the authentication
    print("\n3. Testing authentication with created/existing user...")
    try:
        connection_string = f"mongodb://{username}:{password}@{host}:{port}"
        auth_client = MongoClient(
            connection_string,
            authSource=auth_source,
            serverSelectionTimeoutMS=5000
        )
        
        # Test the connection
        auth_client.admin.command('ping')
        print("   ✓ Authentication successful!")
        
        # Test database access
        db = auth_client[db_name]
        collections = db.list_collection_names()
        print(f"   ✓ Database '{db_name}' accessible with {len(collections)} collections")
        
        auth_client.close()
        return True
        
    except Exception as e:
        print(f"   ✗ Authentication still failing: {e}")
        return False

if __name__ == "__main__":
    success = setup_mongodb_user()
    if success:
        print("\n✅ MongoDB user setup completed successfully!")
        print("You can now run your Django application.")
    else:
        print("\n❌ MongoDB user setup failed.")
        print("Please check the MongoDB configuration and try again.")
