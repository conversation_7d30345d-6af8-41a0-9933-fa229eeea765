from rest_framework import serializers
from .models import (
    ComponentTypeMain, ComponentTypeSub, ComponentType, ComponentGroup,
    Component, StandardComponent, TestingCenterComponent, ManufacturerComponent
)
from mas.models import Standard
from testings.models import TestingCenter
from manufacturer.models import Manufacturer
from mas.serializers import StandardSerializer
from testings.serializers import TestingCenterSerializer
from manufacturer.serializers import ManufacturerSerializer
from RTRDA.serializers import BaseModelSerializer

class ComponentTypeMainSerializer(BaseModelSerializer):
    componentTypeSubs = serializers.SerializerMethodField()

    class Meta:
        model = ComponentTypeMain
        fields = '__all__'

    def get_componentTypeSubs(self, obj):
        # Check if componentTypeSubs was manually added to the object
        if hasattr(obj, 'componentTypeSubs'):
            # Use a simple serializer to avoid circular imports
            return [{'id': sub.id, 'name': sub.name, 'status': sub.status, 'views': sub.views}
                   for sub in obj.componentTypeSubs]
        # Otherwise, fetch from database
        component_type_subs = ComponentTypeSub.objects.filter(componentTypeMain=obj)
        return [{'id': sub.id, 'name': sub.name, 'status': sub.status, 'views': sub.views}
               for sub in component_type_subs]

class ComponentTypeSubSerializer(BaseModelSerializer):
    componentTypeMain = ComponentTypeMainSerializer(read_only=True)
    componentTypeMainId = serializers.PrimaryKeyRelatedField(
        source='componentTypeMain',
        queryset=ComponentTypeMain.objects.all()
    )

    class Meta:
        model = ComponentTypeSub
        fields = '__all__'

class ComponentTypeSerializer(BaseModelSerializer):
    componentTypeMain = ComponentTypeMainSerializer(read_only=True)
    componentTypeSub = ComponentTypeSubSerializer(read_only=True)
    componentTypeMainId = serializers.PrimaryKeyRelatedField(
        source='componentTypeMain',
        queryset=ComponentTypeMain.objects.all()
    )
    componentTypeSubId = serializers.PrimaryKeyRelatedField(
        source='componentTypeSub',
        queryset=ComponentTypeSub.objects.all()
    )

    class Meta:
        model = ComponentType
        fields = '__all__'

class ComponentGroupSerializer(BaseModelSerializer):
    componentTypeMain = ComponentTypeMainSerializer(read_only=True)
    componentTypeSub = ComponentTypeSubSerializer(read_only=True)
    componentType = ComponentTypeSerializer(read_only=True)
    componentTypeMainId = serializers.PrimaryKeyRelatedField(
        source='componentTypeMain',
        queryset=ComponentTypeMain.objects.all()
    )
    componentTypeSubId = serializers.PrimaryKeyRelatedField(
        source='componentTypeSub',
        queryset=ComponentTypeSub.objects.all()
    )
    componentTypeId = serializers.PrimaryKeyRelatedField(
        source='componentType',
        queryset=ComponentType.objects.all()
    )

    class Meta:
        model = ComponentGroup
        fields = '__all__'

class ComponentSerializer(BaseModelSerializer):
    componentTypeMain = ComponentTypeMainSerializer(read_only=True)
    componentTypeSub = ComponentTypeSubSerializer(read_only=True)
    componentType = ComponentTypeSerializer(read_only=True)
    componentGroup = ComponentGroupSerializer(read_only=True)
    componentTypeMainId = serializers.PrimaryKeyRelatedField(
        source='componentTypeMain',
        queryset=ComponentTypeMain.objects.all()
    )
    componentTypeSubId = serializers.PrimaryKeyRelatedField(
        source='componentTypeSub',
        queryset=ComponentTypeSub.objects.all()
    )
    componentTypeId = serializers.PrimaryKeyRelatedField(
        source='componentType',
        queryset=ComponentType.objects.all()
    )
    componentGroupId = serializers.PrimaryKeyRelatedField(
        source='componentGroup',
        queryset=ComponentGroup.objects.all()
    )
    src = serializers.SerializerMethodField()

    class Meta:
        model = Component
        fields = '__all__'
    
    def get_src(self, obj):
        

# Simplified Component serializer for use in relationship serializers
class SimpleComponentSerializer(serializers.ModelSerializer):
    componentTypeMain = ComponentTypeMainSerializer(read_only=True)
    componentTypeSub = ComponentTypeSubSerializer(read_only=True)
    componentType = ComponentTypeSerializer(read_only=True)
    componentGroup = ComponentGroupSerializer(read_only=True)

    class Meta:
        model = Component
        fields = [
            'id', 'componentTypeMain', 'componentTypeSub', 'componentType',
            'componentGroup', 'majorSubSystem', 'majorComponents', 'subComponents',
            'views', 'status', 'createUserId', 'createDate', 'updateUserId', 'updateDate'
        ]

class StandardComponentSerializer(serializers.ModelSerializer):
    standard = StandardSerializer(read_only=True)
    component = SimpleComponentSerializer(read_only=True)
    standardId = serializers.PrimaryKeyRelatedField(
        source='standard',
        queryset=Standard.objects.all()
    )
    componentId = serializers.PrimaryKeyRelatedField(
        source='component',
        queryset=Component.objects.all()
    )

    class Meta:
        model = StandardComponent
        fields = '__all__'

class TestingCenterComponentSerializer(serializers.ModelSerializer):
    testingCenter = TestingCenterSerializer(read_only=True)
    component = SimpleComponentSerializer(read_only=True)
    testingCenterId = serializers.PrimaryKeyRelatedField(
        source='testingCenter',
        queryset=TestingCenter.objects.all()
    )
    componentId = serializers.PrimaryKeyRelatedField(
        source='component',
        queryset=Component.objects.all()
    )

    class Meta:
        model = TestingCenterComponent
        fields = '__all__'

class ManufacturerComponentSerializer(serializers.ModelSerializer):
    manufacturer = ManufacturerSerializer(read_only=True)
    component = SimpleComponentSerializer(read_only=True)
    manufacturerId = serializers.PrimaryKeyRelatedField(
        source='manufacturer',
        queryset=Manufacturer.objects.all()
    )
    componentId = serializers.PrimaryKeyRelatedField(
        source='component',
        queryset=Component.objects.all()
    )

    class Meta:
        model = ManufacturerComponent
        fields = '__all__'