#!/usr/bin/env python3
"""
Test MongoDB connection with the current configuration
"""

import os
from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, OperationFailure
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_mongodb_connection():
    """Test MongoDB connection with current settings"""
    
    # Get MongoDB settings from environment
    host = os.environ.get('MONGODB_HOST', 'localhost')
    port = int(os.environ.get('MONGODB_PORT', 27017))
    username = os.environ.get('MONGODB_USERNAME', '')
    password = os.environ.get('MONGODB_PASSWORD', '')
    auth_source = os.environ.get('MONGODB_AUTH_SOURCE', 'admin')
    db_name = os.environ.get('MONGODB_DB', 'rtrda')
    
    print(f"Testing MongoDB connection with:")
    print(f"  Host: {host}")
    print(f"  Port: {port}")
    print(f"  Username: {username}")
    print(f"  Password: {'*' * len(password) if password else 'None'}")
    print(f"  Auth Source: {auth_source}")
    print(f"  Database: {db_name}")
    print()
    
    try:
        # Test connection without authentication first
        print("1. Testing connection without authentication...")
        client_no_auth = MongoClient(f"mongodb://{host}:{port}", serverSelectionTimeoutMS=5000)
        client_no_auth.admin.command('ping')
        print("   ✓ Connection without auth successful")
        client_no_auth.close()
        
    except Exception as e:
        print(f"   ✗ Connection without auth failed: {e}")
    
    try:
        # Test connection with authentication
        print("2. Testing connection with authentication...")
        if username and password:
            connection_string = f"mongodb://{username}:{password}@{host}:{port}"
            client = MongoClient(
                connection_string,
                authSource=auth_source,
                serverSelectionTimeoutMS=5000
            )
            
            # Test the connection
            client.admin.command('ping')
            print("   ✓ Authentication successful")
            
            # Test database access
            db = client[db_name]
            collections = db.list_collection_names()
            print(f"   ✓ Database '{db_name}' accessible")
            print(f"   ✓ Collections found: {len(collections)}")
            if collections:
                print(f"     - {', '.join(collections[:5])}")
                if len(collections) > 5:
                    print(f"     - ... and {len(collections) - 5} more")
            
            client.close()
            print("\n✓ MongoDB connection test PASSED")
            return True
            
        else:
            print("   ✗ No username/password provided")
            return False
            
    except OperationFailure as e:
        print(f"   ✗ Authentication failed: {e}")
        print("\nPossible solutions:")
        print("1. Check if the username 'sa2' exists in MongoDB")
        print("2. Verify the password is correct")
        print("3. Ensure the user has proper permissions")
        print("4. Try connecting without authentication to check if auth is enabled")
        return False
        
    except ConnectionFailure as e:
        print(f"   ✗ Connection failed: {e}")
        return False
        
    except Exception as e:
        print(f"   ✗ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    test_mongodb_connection()
